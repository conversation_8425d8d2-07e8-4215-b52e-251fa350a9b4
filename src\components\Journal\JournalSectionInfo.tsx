import { useEffect, useState } from "react";
import { FaEdit } from "react-icons/fa";

interface JournalSectionInfoProps {
  sectionInfo: {
    isVisible?: boolean;
    journals?: any[];
  };
}

export default function JournalSectionInfo({ sectionInfo }: JournalSectionInfoProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [sectionDetails, setSectionDetails] = useState<any>({});

  useEffect(() => {
    setSectionDetails({
      isVisible: sectionInfo?.isVisible ?? true,
      journals: sectionInfo?.journals || [],
    });
  }, [sectionInfo]);

  const handleSave = async () => {
    setIsEditing(false);

    const hasChanged = sectionDetails.isVisible !== sectionInfo.isVisible;

    if (!hasChanged) {
      return;
    }

    // In a real implementation, you would save the visibility state
    // For now, we'll just update the local state
    console.log("Saving section visibility:", sectionDetails.isVisible);
  };

  const handleToggleVisibility = () => {
    setSectionDetails({
      ...sectionDetails,
      isVisible: !sectionDetails.isVisible
    });
  };

  return (
    <div className="px-4 py-3 sm:px-8 sm:py-4 relative w-full max-w-md sm:w-fit shadow-md rounded-lg border border-gray-300 bg-white">
      <button
        className="absolute top-2 right-2 text-gray-600 hover:text-gray-800"
        onClick={() => setIsEditing(!isEditing)}
        aria-label="Edit Section"
      >
        <FaEdit className="w-5 h-5 sm:w-4 sm:h-4" />
      </button>
      <div className="space-y-4 sm:space-y-2">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-20">
          <span className="text-gray-700 font-semibold text-base sm:text-sm">Show Section:</span>
          {isEditing ? (
            <div className="w-full sm:w-auto">
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={sectionDetails?.isVisible}
                  onChange={handleToggleVisibility}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
          ) : (
            <span className="text-gray-900 text-base sm:text-sm">
              {sectionDetails?.isVisible ? "Yes" : "No"}
            </span>
          )}
        </div>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-20">
          <span className="text-gray-700 font-semibold text-base sm:text-sm">Total Journals:</span>
          <span className="text-gray-900 text-base sm:text-sm">
            {sectionDetails?.journals?.length || 0}
          </span>
        </div>
        {isEditing && (
          <button
            onClick={handleSave}
            className="w-full mt-2 bg-[#1570EF] text-white p-2 rounded text-base sm:text-sm hover:bg-blue-600 transition-colors"
          >
            Save
          </button>
        )}
      </div>
    </div>
  );
}
