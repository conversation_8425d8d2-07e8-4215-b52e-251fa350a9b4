import React, { useState } from "react";
import { FaEdit, FaTrash } from "react-icons/fa";
import ConfirmationModal from "../common/ConfirmationModal";

interface JournalContentTableProps {
  data: any[];
  fetchHomepageSections: () => void;
  handleEdit: (item: any) => void;
  handleDelete: (item: any) => void;
}

export default function JournalContentTable({
  data,
  fetchHomepageSections,
  handleEdit,
  handleDelete,
}: JournalContentTableProps) {
  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    selectedItem: null as any,
    loading: false
  });

  const handleDeleteClick = (item: any) => {
    setDeleteModal({
      isOpen: true,
      selectedItem: item,
      loading: false
    });
  };

  const handleDeleteConfirm = async () => {
    setDeleteModal(prev => ({ ...prev, loading: true }));
    try {
      await handleDelete(deleteModal.selectedItem);
      setDeleteModal({
        isOpen: false,
        selectedItem: null,
        loading: false
      });
    } catch (error) {
      setDeleteModal(prev => ({ ...prev, loading: false }));
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModal({
      isOpen: false,
      selectedItem: null,
      loading: false
    });
  };

  const headers = ["Position", "Blog Name", "Image", "Actions"];

  return (
    <>
      {/* Table for md and up */}
      <div className="overflow-x-auto bg-white rounded-lg shadow-sm border border-gray-200 hidden md:block">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {headers.map((header, index) => (
                <th
                  key={index}
                  className={`px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                    header === "Position" || header === "Actions"
                      ? "w-[15%]"
                      : header === "Blog Name"
                      ? "w-[50%]"
                      : "w-[20%]"
                  }`}
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100 bg-white">
            {data && data.length > 0 ? (
              data.map((journal, index) => (
                <tr key={index} className="text-left hover:bg-gray-50 transition-colors">
                  <td className="px-4 py-3 text-sm font-medium text-gray-900 w-[15%] text-center">
                    <span className="inline-flex items-center justify-center w-7 h-7 bg-blue-100 text-blue-800 rounded-full text-xs font-semibold">
                      {index + 1}
                    </span>
                  </td>
                  <td className="px-4 py-3 w-[50%]">
                    <div className="flex items-start space-x-4">
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 mb-1">
                          {journal.blogID?.blogName || "N/A"}
                        </div>
                        {journal.blogID?.description && (
                          <div className="text-sm text-gray-500 line-clamp-2" title={journal.blogID.description.replace(/<[^>]*>/g, '')}>
                            {journal.blogID.description.replace(/<[^>]*>/g, '').substring(0, 100)}
                            {journal.blogID.description.length > 100 ? '...' : ''}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-500 w-[20%]">
                    {journal.blogID?.featureImage ? (
                      <img
                        src={journal.blogID.featureImage}
                        alt="Journal"
                        className="w-16 h-12 object-cover rounded-lg shadow-sm"
                      />
                    ) : (
                      <div className="w-16 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                        <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                    )}
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-500 w-[15%]">
                    <div className="flex items-center space-x-1.5">
                      <button
                        onClick={() => handleEdit(journal)}
                        className="inline-flex items-center justify-center w-7 h-7 text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors"
                        title="Edit journal"
                      >
                        <FaEdit className="w-3.5 h-3.5" />
                      </button>
                      <button
                        onClick={() => handleDeleteClick(journal)}
                        className="inline-flex items-center justify-center w-7 h-7 text-red-600 bg-red-50 hover:bg-red-100 rounded-md transition-colors"
                        title="Remove journal"
                      >
                        <FaTrash className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={headers.length} className="text-center py-8 text-gray-500">
                  No Journals selected
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Cards for mobile */}
      <div className="block md:hidden space-y-3">
        {data && data.length > 0 ? (
          data.map((journal, index) => (
            <div
              key={index}
              className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"
            >
              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="inline-flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-xs font-semibold">
                    {index + 1}
                  </span>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleEdit(journal)}
                      className="inline-flex items-center justify-center w-8 h-8 text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
                    >
                      <FaEdit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteClick(journal)}
                      className="inline-flex items-center justify-center w-8 h-8 text-red-600 bg-red-50 hover:bg-red-100 rounded-lg transition-colors"
                    >
                      <FaTrash className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  {journal.blogID?.featureImage ? (
                    <img
                      src={journal.blogID.featureImage}
                      alt="Journal"
                      className="w-16 h-12 object-cover rounded-lg flex-shrink-0"
                    />
                  ) : (
                    <div className="w-16 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-medium text-gray-900 mb-1">
                      {journal.blogID?.blogName || "N/A"}
                    </h3>
                    {journal.blogID?.description && (
                      <p className="text-sm text-gray-500 line-clamp-2">
                        {journal.blogID.description.replace(/<[^>]*>/g, '').substring(0, 80)}
                        {journal.blogID.description.length > 80 ? '...' : ''}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-gray-500 bg-white rounded-lg border border-gray-300">
            No Journals selected
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteModal.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Remove Journal"
        message={`Are you sure you want to remove "${deleteModal.selectedItem?.blogID?.blogName}" from the journal? This action cannot be undone.`}
        confirmText="Remove"
        cancelText="Cancel"
        type="danger"
        loading={deleteModal.loading}
      />
    </>
  );
}
