import { useState, useEffect } from "react";
import { BlogService } from "../../services/blog.service";
import toast from "react-hot-toast";

interface JournalFormProps {
  handleSubmit: (values: any, actions: any) => void;
  selectedJournal: any;
  setUploadError: (error: string | null) => void;
  setUploadSuccess: (success: string | null) => void;
}

export default function JournalForm({
  handleSubmit,
  selectedJournal,
  setUploadError,
  setUploadSuccess,
}: JournalFormProps) {
  const [formData, setFormData] = useState({
    blogName: "",
    description: "",
    featureImage: "",
    handle: "",
    tag: [],
    writerImage: "",
    writerName: "",
    writerShortname: "",
    writerDesignation: "",
    isVisible: true,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imageUploading, setImageUploading] = useState(false);
  const [writerImageUploading, setWriterImageUploading] = useState(false);

  useEffect(() => {
    if (selectedJournal) {
      setFormData({
        blogName: selectedJournal.blogName || "",
        description: selectedJournal.description || "",
        featureImage: selectedJournal.featureImage || "",
        handle: selectedJournal.handle || "",
        tag: selectedJournal.tag || [],
        writerImage: selectedJournal.writerImage || "",
        writerName: selectedJournal.writerName || "",
        writerShortname: selectedJournal.writerShortname || "",
        writerDesignation: selectedJournal.writerDesignation || "",
        isVisible: selectedJournal.isVisible ?? true,
      });
    }
  }, [selectedJournal]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleImageUpload = async (file: File, imageType: 'feature' | 'writer') => {
    try {
      if (imageType === 'feature') {
        setImageUploading(true);
      } else {
        setWriterImageUploading(true);
      }

      let response;
      if (imageType === 'writer') {
        response = await BlogService.uploadWriterImage(file);
      } else {
        response = await BlogService.uploadBlogImage(file);
      }

      if (response.data && response.data.success) {
        const imageUrl = response.data.data?.url || response.data.url;
        
        setFormData(prev => ({
          ...prev,
          [imageType === 'feature' ? 'featureImage' : 'writerImage']: imageUrl
        }));
        
        setUploadSuccess("Image uploaded successfully");
        toast.success("Image uploaded successfully");
      } else {
        throw new Error(response.data?.message || "Upload failed");
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || "Upload failed";
      setUploadError(errorMessage);
      toast.error(errorMessage);
    } finally {
      if (imageType === 'feature') {
        setImageUploading(false);
      } else {
        setWriterImageUploading(false);
      }
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, imageType: 'feature' | 'writer') => {
    const file = e.target.files?.[0];
    if (file) {
      handleImageUpload(file, imageType);
    }
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const actions = {
      setSubmitting: setIsSubmitting,
      resetForm: () => {
        setFormData({
          blogName: "",
          description: "",
          featureImage: "",
          handle: "",
          tag: [],
          writerImage: "",
          writerName: "",
          writerShortname: "",
          writerDesignation: "",
          isVisible: true,
        });
      }
    };

    await handleSubmit(formData, actions);
  };

  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div>
        <label htmlFor="blogName" className="block text-sm font-medium text-gray-700">
          Blog Name
        </label>
        <input
          type="text"
          id="blogName"
          name="blogName"
          value={formData.blogName}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          required
        />
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Description
        </label>
        <textarea
          id="description"
          name="description"
          rows={3}
          value={formData.description}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="featureImage" className="block text-sm font-medium text-gray-700">
          Feature Image
        </label>
        <input
          type="file"
          id="featureImage"
          accept="image/*"
          onChange={(e) => handleFileChange(e, 'feature')}
          className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          disabled={imageUploading}
        />
        {imageUploading && <p className="text-sm text-blue-600 mt-1">Uploading...</p>}
        {formData.featureImage && (
          <img src={formData.featureImage} alt="Feature" className="mt-2 w-20 h-20 object-cover rounded" />
        )}
      </div>

      <div>
        <label htmlFor="writerName" className="block text-sm font-medium text-gray-700">
          Writer Name
        </label>
        <input
          type="text"
          id="writerName"
          name="writerName"
          value={formData.writerName}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="writerShortname" className="block text-sm font-medium text-gray-700">
          Writer Short Name
        </label>
        <input
          type="text"
          id="writerShortname"
          name="writerShortname"
          value={formData.writerShortname}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="writerImage" className="block text-sm font-medium text-gray-700">
          Writer Image
        </label>
        <input
          type="file"
          id="writerImage"
          accept="image/*"
          onChange={(e) => handleFileChange(e, 'writer')}
          className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          disabled={writerImageUploading}
        />
        {writerImageUploading && <p className="text-sm text-blue-600 mt-1">Uploading...</p>}
        {formData.writerImage && (
          <img src={formData.writerImage} alt="Writer" className="mt-2 w-20 h-20 object-cover rounded" />
        )}
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="isVisible"
          name="isVisible"
          checked={formData.isVisible}
          onChange={handleInputChange}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="isVisible" className="ml-2 block text-sm text-gray-900">
          Visible
        </label>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="submit"
          disabled={isSubmitting || imageUploading || writerImageUploading}
          className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Updating...
            </>
          ) : (
            'Update Journal'
          )}
        </button>
      </div>
    </form>
  );
}
