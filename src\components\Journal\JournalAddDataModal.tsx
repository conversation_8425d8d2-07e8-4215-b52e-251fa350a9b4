import React, { useEffect, useState } from 'react';
import { BlogService } from '../../services/blog.service';
import toast from 'react-hot-toast';

interface JournalAddDataModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (selectedBlogs: any[]) => void;
  existingJournals?: any[];
}

export default function JournalAddDataModal({
  isOpen,
  onClose,
  onAdd,
  existingJournals = []
}: JournalAddDataModalProps) {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [blogsData, setBlogsData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Get existing blog IDs to show which are already in journal
  const existingBlogIds = existingJournals.map(journal => journal.blogID._id || journal.blogID);

  const GetAllJournals = async () => {
    try {
      setLoading(true);

      // First try with pagination
      let response = await BlogService.getAllBlogs(1, 50);

      if (response.data && response.data.success) {
        // Handle different possible response structures
        const responseData = response.data.data;

        if (Array.isArray(responseData)) {
          // If data is directly an array of blogs
          if (responseData.length === 0) {
            // If empty, try without pagination
            console.log("Paginated response empty, trying without pagination...");
            response = await BlogService.getAllBlogsNoPagination();
            const noPagData = response.data?.data;
            if (Array.isArray(noPagData)) {
              setBlogsData(noPagData);
            } else {
              setBlogsData([]);
            }
          } else {
            setBlogsData(responseData);
          }
        } else if (responseData && responseData.blogs) {
          // If data has blogs property
          setBlogsData(responseData.blogs || []);
        } else {
          // Fallback - try without pagination
          console.log("Unexpected response structure, trying without pagination...");
          response = await BlogService.getAllBlogsNoPagination();
          const fallbackData = response.data?.data;
          if (Array.isArray(fallbackData)) {
            setBlogsData(fallbackData);
          } else {
            setBlogsData([]);
          }
        }
      } else {
        throw new Error(response.data?.message || "Failed to fetch blogs");
      }
    } catch (error) {
      console.error("Error fetching blogs:", error);
      toast.error("Failed to fetch blogs");
      setBlogsData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      GetAllJournals();
      setSelectedItems([]); // Reset selection when modal opens
    }
  }, [isOpen]);

  const handleSelectItem = (blogId: string) => {
    setSelectedItems(prevSelected => {
      if (prevSelected.includes(blogId)) {
        return prevSelected.filter(id => id !== blogId);
      } else {
        return [...prevSelected, blogId];
      }
    });
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // Get the selected blog objects
      const selectedBlogs = blogsData.filter(blog => selectedItems.includes(blog._id));

      // Call the parent's onAdd function with selected blogs
      onAdd(selectedBlogs);

      toast.success("Journals added successfully!");
      onClose();
      setSelectedItems([]);
    } catch (error) {
      console.error("Error saving journals:", error);
      toast.error("Failed to save journals");
    } finally {
      setSaving(false);
    }
  };

  const handleDiscard = () => {
    setSelectedItems([]);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>
        
        <div className="relative transform overflow-hidden rounded-lg bg-white shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-4xl">
          <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                <h3 className="text-base font-semibold leading-6 text-gray-900 mb-4">
                  Select Journals
                </h3>
                
                {loading ? (
                  <div className="flex justify-center items-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span className="ml-2 text-gray-600">Loading blogs...</span>
                  </div>
                ) : (
                  <div className="data-list overflow-y-auto my-4 flex flex-col gap-2 min-h-[60vh] max-h-[60vh] rounded-lg border border-gray-300 p-2">
                    {blogsData.length > 0 ? (
                      <div>
                        {/* Desktop View */}
                        <div className="hidden md:flex md:flex-col md:gap-3">
                          {blogsData.map((item) => {
                            const isAlreadyInJournal = existingBlogIds.includes(item._id);
                            const isSelected = selectedItems.includes(item._id);

                            return (
                              <div
                                key={item._id}
                                className={`item flex items-center gap-3 p-4 border rounded-lg transition-all cursor-pointer
                                ${isSelected
                                  ? 'bg-blue-50 border-blue-200 shadow-sm'
                                  : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
                                }`}
                                onClick={() => handleSelectItem(item._id)}
                              >
                                <input
                                  type="checkbox"
                                  checked={isSelected}
                                  onChange={() => handleSelectItem(item._id)}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                              <div className="flex items-center gap-3 flex-1">
                                <div className="flex-shrink-0">
                                  {item.featureImage ? (
                                    <img
                                      src={item.featureImage}
                                      alt="Blog"
                                      className="w-12 h-12 object-cover rounded-md"
                                    />
                                  ) : (
                                    <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center">
                                      <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                      </svg>
                                    </div>
                                  )}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2 mb-1">
                                    <h4 className='font-medium text-gray-900 truncate'>{item.blogName}</h4>
                                    {isAlreadyInJournal && (
                                      <span className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full flex-shrink-0">
                                        In journal
                                      </span>
                                    )}
                                  </div>
                                  <p className='text-gray-500 text-sm line-clamp-1'>
                                    {item.description ?
                                      item.description.replace(/<[^>]*>/g, '').substring(0, 80) + (item.description.length > 80 ? '...' : '')
                                      : 'No description available'
                                    }
                                  </p>
                                </div>
                              </div>
                            </div>
                            );
                          })}
                        </div>

                        {/* Mobile View */}
                        <div className="md:hidden flex flex-col gap-3">
                          {blogsData.map((item) => {
                            const isAlreadyInJournal = existingBlogIds.includes(item._id);
                            const isSelected = selectedItems.includes(item._id);

                            return (
                              <div
                                key={item._id}
                                className={`flex items-center gap-3 p-3 bg-white rounded-lg border cursor-pointer transition-all ${
                                  isSelected
                                    ? 'border-blue-200 bg-blue-50'
                                    : 'border-gray-200 hover:bg-gray-50'
                                }`}
                                onClick={() => handleSelectItem(item._id)}
                              >
                                <input
                                  type="checkbox"
                                  checked={isSelected}
                                  onChange={() => handleSelectItem(item._id)}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded flex-shrink-0"
                                />
                                <div className="flex items-center gap-3 flex-1 min-w-0">
                                  {item.featureImage ? (
                                    <img
                                      src={item.featureImage}
                                      alt="Blog"
                                      className="w-10 h-10 object-cover rounded-md flex-shrink-0"
                                    />
                                  ) : (
                                    <div className="w-10 h-10 bg-gray-100 rounded-md flex items-center justify-center flex-shrink-0">
                                      <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                      </svg>
                                    </div>
                                  )}
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-center gap-2 mb-1">
                                      <h4 className="font-medium text-gray-900 truncate">{item.blogName}</h4>
                                      {isAlreadyInJournal && (
                                        <span className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full flex-shrink-0">
                                          In journal
                                        </span>
                                      )}
                                    </div>
                                    <p className="text-gray-500 text-sm truncate">
                                      {item.description ?
                                        item.description.replace(/<[^>]*>/g, '').substring(0, 60) + (item.description.length > 60 ? '...' : '')
                                        : 'No description'
                                      }
                                    </p>
                                  </div>
                                </div>
                            );
                          })}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                          <path d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.713-3.714M14 40v-4c0-1.313.253-2.566.713-3.714m0 0A10.003 10.003 0 0124 26c4.21 0 7.813 2.602 9.288 6.286M30 14a6 6 0 11-12 0 6 6 0 0112 0zm12 6a4 4 0 11-8 0 4 4 0 018 0zm-28 0a4 4 0 11-8 0 4 4 0 018 0z" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No blogs available</h3>
                        <p className="mt-1 text-sm text-gray-500">
                          Create some blog posts first to add them to your journal.
                        </p>
                        <div className="mt-4">
                          <a
                            href="/blogs/create"
                            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                            onClick={() => onClose()}
                          >
                            Create Blog Post
                          </a>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
            <button
              type="button"
              className="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 sm:ml-3 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleSave}
              disabled={saving}
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                `Save (${selectedItems.length} selected)`
              )}
            </button>
            <button
              type="button"
              className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
              onClick={handleDiscard}
              disabled={saving}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
