import makeRequest from "../api/makeRequest";
import { RequestMethods } from "../api/requestMethode";

export class BlogService {
  // Get all blogs with pagination
  static async getAllBlogs(page: number = 1, limit: number = 5) {
    const endpoint = `/blogs?page=${page}&limit=${limit}`;
    return await makeRequest(endpoint, RequestMethods.GET);
  }

  // Get all blogs without pagination (for testing)
  static async getAllBlogsNoPagination() {
    return await makeRequest("/blogs", RequestMethods.GET);
  }

  // Get single blog by handle
  static async getBlogByHandle(handle: string) {
    return await makeRequest(`/blogs/${handle}`, RequestMethods.GET);
  }

  // Get single blog by ID
  static async getBlogById(id: string) {
    return await makeRequest(`/blogs/${id}`, RequestMethods.GET);
  }

  // Alternative endpoint for getting blog by ID
  static async getBlogByIdAlt(id: string) {
    return await makeRequest(`/blog/${id}`, RequestMethods.GET);
  }

  // Create new blog
  static async createBlog(blogData: any) {
    return await makeRequest("/blogs", RequestMethods.POST, blogData);
  }

  // Update existing blog
  static async updateBlog(handle: string, blogData: any) {
    return await makeRequest(`/blogs/${handle}`, RequestMethods.PUT, blogData);
  }

  // Delete blog
  static async deleteBlog(handle: string) {
    return await makeRequest(`/blogs/${handle}`, RequestMethods.DELETE);
  }

  // Upload blog cover image
  static async uploadBlogImage(file: File) {
    const formData = new FormData();
    formData.append("image", file);
    return await makeRequest("/blogs/upload/blog-image", RequestMethods.POST, formData);
  }

  // Upload writer image
  static async uploadWriterImage(file: File) {
    const formData = new FormData();
    formData.append("image", file);
    return await makeRequest("/blogs/upload/writer-image", RequestMethods.POST, formData);
  }

  // Delete image
  static async deleteImage(imageUrl: string) {
    return await makeRequest("/blogs/image/delete", RequestMethods.DELETE, { imageUrl });
  }

  // Legacy upload file method (keeping for backward compatibility)
  static async uploadFile(file: File) {
    return this.uploadBlogImage(file);
  }
}
