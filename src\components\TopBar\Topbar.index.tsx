import { But<PERSON>, Container, Nav, Navbar, NavDropdown } from "react-bootstrap";
import { useLocation, useNavigate } from "react-router-dom";
import "./Topbar.css"

interface ITopbar {
  menuData: any;
}

export default function Topbar({ menuData }: ITopbar) {
  const navigate = useNavigate();
  const location = useLocation();

  const showAllowedMenu = menuData.filter(
    (routes: any) => routes.navbarShow === true
  );

  const handleLogout = () => {
    sessionStorage.removeItem("authKey");
    navigate("/login");
  };

  return (
    <>
      <Navbar
        expand="lg"
        className="mb-3 p-3 shadow"
      >
        <Container>
          <Navbar.Brand
            href="/dashboard"
            style={{
              fontSize: "20px",
            }}
          >
            TP Admin
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse
            id="basic-navbar-nav"
            className="justify-content-between"
          >
            <Nav className="mr-auto me-auto nav-links-font"> {/* Add the mr-auto class */}
              {showAllowedMenu.map((data: any, index: number) => {
                if (data.children && data.children.length > 0) {
                  return (
                    <NavDropdown
                      key={index}
                      title={data.name}
                      id={`nav-dropdown-${index}`}
                      className="xrg-nav-link"
                    >
                      {data.children.map((child: any, childIndex: number) => (
                        <NavDropdown.Item
                          key={childIndex}
                          onClick={() => navigate(child.path)}
                          className={
                            child.path === location.pathname.split("/")[1]
                              ? "xrg-nav-selected"
                              : ""
                          }
                        >
                          {child.name}
                        </NavDropdown.Item>
                      ))}
                    </NavDropdown>
                  );
                } else {
                  return (
                    <div
                      key={index}
                      onClick={() => navigate(data.path)}
                    >
                      <Nav.Link className={
                        "xrg-nav-link" +
                        (data.path === location.pathname.split("/")[1]
                          ? " xrg-nav-selected"
                          : " ")
                      }>
                        {data.name}
                      </Nav.Link>
                    </div>
                  );
                }
              })}
            </Nav>
            <Button
              variant="secondary"
              className="text-white ml-2 justify-self-end"
              onClick={handleLogout}

            >
              Logout
            </Button>
          </Navbar.Collapse>
        </Container>
      </Navbar>
    </>
  );
}
