import React, { useEffect, useState } from "react";
import JournalSectionInfo from "../../../components/Journal/JournalSectionInfo";
import JournalContentTable from "../../../components/Journal/JournalContentTable";
import JournalAddDataModal from "../../../components/Journal/JournalAddDataModal";
import { BlogService } from "../../../services/blog.service";
import JournalForm from "../../../components/Journal/JournalForm";
import ConfirmationModal from "../../../components/common/ConfirmationModal";
import toast from "react-hot-toast";

export default function Journal() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [journalData, setJournalData] = useState<any>({});
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedJournal, setSelectedJournal] = useState<any>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  // Fetch journal section data from journal API
  const fetchJournalSection = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await JournalService.getJournalSection();

      if (response.data && response.data.success) {
        const sectionData = response.data.data;
        setJournalData({
          _id: sectionData._id,
          section: sectionData.section,
          isVisible: sectionData.isVisible,
          heading: sectionData.heading,
          subHeading: sectionData.subHeading,
          journals: sectionData.journals || []
        });
      } else {
        throw new Error(response.data?.message || "Failed to fetch journal data");
      }

    } catch (error: any) {
      console.error("Error fetching journal section:", error);
      setError(error.response?.data?.message || error.message || "Failed to fetch journal data");
    } finally {
      setLoading(false);
    }
  };

  const handleAddJournal = async (selectedBlogs: any[]) => {
    try {
      // Get existing blog IDs and journals
      const existingBlogIds = journalData.journals?.map(journal => journal.blogID._id || journal.blogID) || [];
      const selectedBlogIds = selectedBlogs.map(blog => blog._id);

      // Find blogs to add (selected but not in journal)
      const blogsToAdd = selectedBlogs.filter(blog => !existingBlogIds.includes(blog._id));

      // Find blogs to remove (in journal but not selected)
      const blogsToRemove = journalData.journals?.filter(journal => {
        const blogId = journal.blogID._id || journal.blogID;
        return existingBlogIds.includes(blogId) && !selectedBlogIds.includes(blogId);
      }) || [];

      // Create new journal entries for blogs to add
      const newJournals = blogsToAdd.map((blog, index) => ({
        blogID: blog,
        position: (journalData.journals?.length || 0) + index + 1
      }));

      // Remove blogs that were unselected and add new ones
      const updatedJournals = [
        ...(journalData.journals?.filter(journal => {
          const blogId = journal.blogID._id || journal.blogID;
          return selectedBlogIds.includes(blogId);
        }) || []),
        ...newJournals
      ];

      setJournalData(prev => ({
        ...prev,
        journals: updatedJournals
      }));

      const addedCount = blogsToAdd.length;
      const removedCount = blogsToRemove.length;

      let message = '';
      if (addedCount > 0 && removedCount > 0) {
        message = `${addedCount} journal${addedCount > 1 ? 's' : ''} added, ${removedCount} removed!`;
      } else if (addedCount > 0) {
        message = `${addedCount} journal${addedCount > 1 ? 's' : ''} added successfully!`;
      } else if (removedCount > 0) {
        message = `${removedCount} journal${removedCount > 1 ? 's' : ''} removed successfully!`;
      } else {
        message = "No changes made.";
      }

      // Update the journal section via API
      const updateData = {
        section: "journal",
        isVisible: journalData.isVisible,
        heading: journalData.heading,
        subHeading: journalData.subHeading,
        journals: updatedJournals.map(journal => ({
          blogID: journal.blogID._id || journal.blogID,
          position: journal.position
        }))
      };

      const response = await JournalService.updateJournalSection(updateData);

      if (response.data && response.data.success) {
        // Refresh the data from server
        await fetchJournalSection();
        toast.success(message);
        setMessage(message);
        setSuccess(true);
      } else {
        throw new Error(response.data?.message || "Failed to update journal section");
      }

      // Close the modal
      setIsAddModalOpen(false);

    } catch (error: any) {
      console.error("Error updating journals:", error);
      setError(error.message || "Failed to update journals");
    }
  };

  useEffect(() => {
    fetchJournalSection();
  }, []);

  const handleEdit = (data: any) => {
    setSelectedJournal(data);
    setIsEditModalOpen(true);
  };

  const handleSubmit = async (values: any, { setSubmitting, resetForm }: any) => {
    try {
      setSubmitting(true);
      setError(null);
      setSuccess(null);
      setMessage(null);

      const { _id, handle, createdAt, updatedAt, ...updatedData } = values;

      const response = await BlogService.updateBlog(
        selectedJournal?.blogID?._id,
        updatedData
      );

      if (response.data && response.data.success) {
        setSuccess(true);
        setMessage(response.data.message || "Journal updated successfully");
        fetchJournalSection();
        setIsEditModalOpen(false);
        toast.success(response.data.message || "Journal updated successfully");
      } else {
        throw new Error(response.data?.message || "Failed to update journal");
      }
      resetForm();
    } catch (err: any) {
      console.error("Error occurred while updating the journal", err);
      setError(true);
      const errorMessage = err.response?.data?.message || err.message || "Something went wrong while updating the journal";
      setMessage(errorMessage);
      toast.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteJournal = async (journalItem: any) => {
    try {
      // Remove the journal from the list
      const updatedJournals = journalData.journals
        .filter((journal: any) => journal.blogID?._id !== journalItem.blogID?._id)
        .map((journal: any, index: number) => ({
          blogID: journal.blogID._id,
          position: index
        }));

      // Update via API
      const updateData = {
        section: "journal",
        isVisible: journalData.isVisible,
        heading: journalData.heading,
        subHeading: journalData.subHeading,
        journals: updatedJournals
      };

      const response = await JournalService.updateJournalSection(updateData);

      if (response.data && response.data.success) {
        toast.success("Journal removed successfully");
        fetchJournalSection();
      } else {
        throw new Error(response.data?.message || "Failed to remove journal");
      }
    } catch (error) {
      console.error("Error deleting journal:", error);
      toast.error("Failed to remove journal");
    }
  };

  useEffect(() => {
    if (error || uploadError) {
      const errorMessage = message || uploadError || "An error occurred";
      toast.error(errorMessage);
    }
  }, [error, uploadError, message]);

  useEffect(() => {
    if (success || uploadSuccess) {
      const successMessage = success ? message : uploadSuccess || "Operation completed successfully";
      toast.success(successMessage);
    }
  }, [success, uploadSuccess, message]);

  return (
    <div className="">
      <div className="sm:flex sm:flex-col gap-4">
        <div className="sm:flex-auto text-left">
          <h1 className="text-base font-semibold text-gray-900">
            The Picklebay Journal
          </h1>
        </div>
        <div className="flex items-start md:items-center justify-between w-full flex-col md:flex-row gap-4 mt-2">
          <JournalSectionInfo sectionInfo={journalData} />
          <div className="flex justify-end">
            <button
              type="button"
              className="block rounded-md bg-[#1570EF] px-3 py-2 text-center text-sm font-semibold text-white shadow-xs hover:bg-[#1570EF] focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[#1570EF]"
              onClick={() => setIsAddModalOpen(true)}
            >
              Add Journal
            </button>
          </div>
        </div>
      </div>
      
      {loading ? (
        <div className="mt-8 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <p className="mt-2 text-gray-600">Loading journals...</p>
        </div>
      ) : error ? (
        <div className="mt-8 text-center">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading journals</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
                <div className="mt-4">
                  <button
                    onClick={fetchJournalSection}
                    className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                  >
                    Try again
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (!journalData.journals || journalData.journals.length === 0) ? (
        <div className="mt-8 text-center">
          <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.713-3.714M14 40v-4c0-1.313.253-2.566.713-3.714m0 0A10.003 10.003 0 0124 26c4.21 0 7.813 2.602 9.288 6.286M30 14a6 6 0 11-12 0 6 6 0 0112 0zm12 6a4 4 0 11-8 0 4 4 0 018 0zm-28 0a4 4 0 11-8 0 4 4 0 018 0z" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No journals available</h3>
            <p className="mt-1 text-sm text-gray-500">
              {message || "Get started by creating some blog posts first, then add them to your journal."}
            </p>
            <div className="mt-6 flex justify-center space-x-3">
              <a
                href="/blogs/create"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
                Create Blog Post
              </a>
              <a
                href="/blogs"
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                View All Blogs
              </a>
            </div>
          </div>
        </div>
      ) : (
        <div className="mt-8 flow-root">
          <div className="">
            <div className="inline-block min-w-full w-full py-2 align-middle">
              <JournalContentTable
                data={journalData.journals || []}
                fetchHomepageSections={fetchJournalSection}
                handleEdit={handleEdit}
                handleDelete={handleDeleteJournal}
              />
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {isEditModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setIsEditModalOpen(false)}></div>

            <div className="relative transform overflow-hidden rounded-lg bg-white shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl">
              <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <h3 className="text-base font-semibold leading-6 text-gray-900 mb-4">
                      Edit Journal Details
                    </h3>
                    <JournalForm
                      handleSubmit={handleSubmit}
                      selectedJournal={selectedJournal?.blogID ?? null}
                      setUploadError={setUploadError}
                      setUploadSuccess={setUploadSuccess}
                    />
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <button
                  type="button"
                  className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                  onClick={() => setIsEditModalOpen(false)}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Journal Modal */}
      <JournalAddDataModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddJournal}
        existingJournals={journalData.journals || []}
      />
    </div>
  );
}
