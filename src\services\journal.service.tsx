import makeRequest from "../api/makeRequest";
import { RequestMethods } from "../api/requestMethode";

export class JournalService {
  // Get journal section data
  static async getJournalSection() {
    return await makeRequest("/homepage-sections?section=journal", RequestMethods.GET);
  }

  // Update journal section
  static async updateJournalSection(data: any) {
    return await makeRequest("/homepage-sections/journal", RequestMethods.POST, data);
  }

  // Get all public blogs for journal selection
  static async getPublicBlogs() {
    return await makeRequest("/blogs", RequestMethods.GET);
  }
}
