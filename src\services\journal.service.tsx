import makeRequest from "../api/makeRequest";
import { RequestMethods } from "../api/requestMethode";

export class JournalService {
  // Get journal section data
  static async getJournalSection() {
    return await makeRequest("/homepage-sections/type/journal", RequestMethods.GET);
  }

  // Get all homepage sections
  static async getAllHomepageSections() {
    return await makeRequest("/homepage-sections", RequestMethods.GET);
  }

  // Update journal section
  static async updateJournalSection(sectionId: string, data: any) {
    return await makeRequest(`/homepage-sections/${sectionId}`, RequestMethods.PUT, data);
  }

  // Create journal section (if needed)
  static async createJournalSection(data: any) {
    return await makeRequest("/homepage-sections", RequestMethods.POST, data);
  }

  // Delete journal from section
  static async deleteJournalFromSection(sectionId: string, journalId: string) {
    return await makeRequest(`/homepage-sections/${sectionId}/journals/${journalId}`, RequestMethods.DELETE);
  }
}
